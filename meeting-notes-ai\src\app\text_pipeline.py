from .summarize import summarize
from .todo import extract_actions
from .classify_mock import classify       
from .translate import translate

def run_text_pipeline(zh_text: str, summary_style="paragraph"):
    """返回 dict 与 run_pipeline 保持一致"""
    summary = summarize(zh_text, style=summary_style)
    summary_en = translate(summary, src="zh", tgt="en")
    todos   = extract_actions(summary)
    mtype   = classify(summary)

    return {
        "source_text": zh_text.strip(),
        "segments": [],  
        "summary": summary,
        "summary_en": summary_en,
        "todos": todos,
        "meeting_type": mtype,
    }


