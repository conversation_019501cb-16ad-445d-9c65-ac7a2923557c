"""
Zero-shot meeting-type classifier using BART-MNLI.
"""

from functools import lru_cache
from typing import List, Dict

from transformers import pipeline

# 可根据需要增删标签
_MEETING_TYPES = [
    "scrum_meeting",
    "sales_demo",
    "brainstorming",
    "one_on_one",
    "training_session",
]

@lru_cache(maxsize=1)
def _get_clf():
    return pipeline(
        "zero-shot-classification",
        model="facebook/bart-large-mnli",
        device=-1,
    )

def classify(text: str, labels: List[str] = None) -> Dict[str, float]:
    """
    返回 {label: score} 字典；score 越大越可能。
    """
    if labels is None:
        labels = _MEETING_TYPES
    res = _get_clf()(text, candidate_labels=labels)
    return dict(zip(res["labels"], res["scores"]))

def classify_meeting(text: str) -> dict:
    """
    包装层：直接复用上面的 classify() 结果。
    留着这里是为了跟其它模块同名；以后如果想拆分不同模型，
    只改这里的实现即可，pipeline 不用动。
    """
    return classify(text) 