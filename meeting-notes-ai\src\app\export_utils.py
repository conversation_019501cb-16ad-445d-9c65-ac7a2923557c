from docx import Document
from datetime import datetime
from pathlib import Path
import uuid, json, tempfile
from reportlab.lib.pagesizes import A4
from reportlab.pdfgen import canvas
from reportlab.lib.units import cm


def export_docx(summary: str, todos: list, meeting_type: str) -> str:
    """
    将摘要 + 行动项导出为 docx，返回生成的文件路径
    todos: list[dict] 形如 {"action":..., "owner":..., "due":...}
    """
    doc = Document()
    doc.add_heading("会议纪要", level=1)
    doc.add_paragraph(f"类型：{meeting_type}")
    doc.add_paragraph(f"生成时间：{datetime.now():%Y-%m-%d %H:%M}")

    doc.add_heading("摘要", level=2)
    for para in summary.split("\n"):
        doc.add_paragraph(para.strip())

    doc.add_heading("待办事项", level=2)
    table = doc.add_table(rows=1, cols=3)
    hdr_cells = table.rows[0].cells
    hdr_cells[0].text, hdr_cells[1].text, hdr_cells[2].text = "Action", "Owner", "Due"

    for t in todos:
        row = table.add_row().cells
        row[0].text = t.get("action", "")
        row[1].text = t.get("owner", "")
        row[2].text = t.get("due", "")

    # 保存到临时文件夹，文件名带 uuid 避免冲突
    tmp_dir = Path(tempfile.gettempdir())
    file_path = tmp_dir / f"meeting_{uuid.uuid4().hex}.docx"
    doc.save(file_path)
    return str(file_path)

def export_pdf(summary: str, todos: list, meeting_type: str):
    """
    生成 PDF，返回文件路径
    """
    tmp_path = Path(tempfile.gettempdir()) / f"{uuid.uuid4().hex}.pdf"
    c = canvas.Canvas(str(tmp_path), pagesize=A4)
    width, height = A4

    def draw_multiline(text, x, y, max_width):
        """简单自动换行"""
        from reportlab.pdfbase.pdfmetrics import stringWidth
        words = text.split()
        line  = ""
        for w in words:
            if stringWidth(line + w, "Helvetica", 11) < max_width:
                line += w + " "
            else:
                c.drawString(x, y, line)
                y -= 14
                line = w + " "
        c.drawString(x, y, line)
        return y - 18

    y = height - 2*cm
    c.setFont("Helvetica-Bold", 16)
    c.drawString(2*cm, y, "会议纪要")
    y -= 20
    c.setFont("Helvetica", 11)
    c.drawString(2*cm, y, f"类型：{meeting_type}")
    y -= 14
    c.drawString(2*cm, y, f"生成时间：{datetime.now():%Y-%m-%d %H:%M}")
    y -= 24

    c.setFont("Helvetica-Bold", 13)
    c.drawString(2*cm, y, "摘要")
    y -= 18
    c.setFont("Helvetica", 11)
    for para in summary.split("\n"):
        y = draw_multiline(para.strip(), 2*cm, y, width - 4*cm)

    c.setFont("Helvetica-Bold", 13)
    y -= 10
    c.drawString(2*cm, y, "待办事项")
    y -= 18
    c.setFont("Helvetica", 11)
    for t in todos:
        line = f"· {t['action']}  (Owner: {t['owner'] or '-'}  Due: {t['due'] or '-'})"
        y = draw_multiline(line, 2*cm, y, width - 4*cm)

    c.save()
    return str(tmp_path)