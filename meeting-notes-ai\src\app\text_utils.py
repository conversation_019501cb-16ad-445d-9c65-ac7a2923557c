"""
text_utils.py
-------------
常用文本工具函数（纯标准库，无第三方依赖）
"""

import re
from typing import List

__all__ = [
    "clean_text",
    "split_cn_sentences",
    "truncate_tokens",
    "make_bullets",
]

# ----------------------------------------------------------------------
def clean_text(text: str) -> str:
    """
    去掉多余空白与全角空格，统一换行。

    >>> clean_text("  你好，  世界！  \n\n")
    '你好， 世界！'
    """
    if not text:
        return ""
    # 全角空格 → 半角
    text = text.replace("\u3000", " ")
    # 连续空白折叠
    text = re.sub(r"[ \t]+", " ", text)
    # 去两端空白 & 连续换行压缩
    text = re.sub(r"\s*\n\s*", "\n", text).strip()
    return text


# ----------------------------------------------------------------------
def split_cn_sentences(text: str) -> List[str]:
    """
    粗粒度中文断句。按 。！？ 分割，保留结束符。
    """
    text = clean_text(text)
    if not text:
        return []
    # 正则分割并保留标点
    segs = re.split(r"([。！？])", text)
    sentences = [
        (segs[i] + segs[i + 1]).strip()
        for i in range(0, len(segs) - 1, 2)
    ]
    return [s for s in sentences if s]


# ----------------------------------------------------------------------
def truncate_tokens(text: str, max_tokens: int = 512) -> str:
    """
    非 GPT 情况下的简易「按字数截断」。
    你可以 later 用 tiktoken 代替。

    >>> truncate_tokens("abcdef", 4)
    'abcd'
    """
    return text if len(text) <= max_tokens else text[: max_tokens]


# ----------------------------------------------------------------------
def make_bullets(
    lines: List[str],
    bullet_char: str = "·",
) -> str:
    """
    将字符串列表合成要点式文本。

    >>> make_bullets(["第一", "第二"])
    '· 第一\\n· 第二'
    """
    return "\n".join(f"{bullet_char} {l.strip()}" for l in lines if l.strip())
