from .gpt_utils_mock import gpt_chat

def summarize(text: str, style: str = "paragraph") -> str:
    """
    参数
    ----
    text  : 英文转写全文
    style : "paragraph" | "bullet"
    """
    if style == "bullet":
        prompt = (
            "请用 **中文要点式** 列出 3-6 条本次会议摘要，用 '· ' 开头。"
            "不要加标题，不要编号，保持简洁。"
        )
    else:  # paragraph
        prompt = (
            "请用 **中文段落式**（约 150-200 字）写出本次会议摘要，"
            "包含主题、关键讨论要点、结论。"
        )

def summarize(text: str, style: str = "paragraph") -> str:
    if style == "bullet":
        user = "请用要点式列出会议摘要"
    else:
        user = "请用段落式总结会议"
    return gpt_chat("", user, lang="zh")