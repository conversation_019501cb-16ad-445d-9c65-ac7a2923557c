"""
Speaker diarization wrapper around pyannote.audio.
首次调用时才真正加载 / 下载模型，避免 import 阶段就失败。
"""
from functools import lru_cache
from pyannote.audio import Pipeline


@lru_cache(maxsize=1)
def get_pipeline():
    """Lazy-load pipeline once. 自动使用已登录的 Hugging Face token。"""
    try:
        return Pipeline.from_pretrained(
            "pyannote/speaker-diarization@2.1",
            use_auth_token=True,          # 必须 True 才会带 token
        )
    except Exception as exc:
        raise RuntimeError(
            "❌ 载入 speaker-diarization 模型失败，请检查网络或 Hugging Face token。"
        ) from exc


def diarize(audio_path: str):
    """
    Parameters
    ----------
    audio_path : str

    Returns
    -------
    list[(start, end, speaker)]
    """
    pipeline = get_pipeline()          # 若失败会抛 RuntimeError
    diar = pipeline(audio_path)

    segments = [
        (turn.start, turn.end, spk)
        for turn, _, spk in diar.itertracks(yield_label=True)
    ]
    return segments