# 用户故事脚本 v0.9
> 产品：Meeting-Notes AI（WoZ 演示用）  
> 角色：  
> · “用户”——被试；  
> · “系统”——前端 UI + 巫师（后台手动/Mock）。

---

## 场景 1：上传会议录音 → 自动摘要 & 待办

### 前置条件
* 用户已经在首页看到「上传音频」按钮。
* 巫师端已准备好对该音频的预设回复（见 CSV）。

### 对话步骤
1. **用户**：点击「上传」，选中 `demo_meeting.wav`  
   **系统**：显示进度条 0–100%，约 5 秒。  
   **巫师**：什麼都不用做——Mock 会返回：
   * 中文摘要（5 行要点）
   * 英文摘要
   * 两条待办
   * 会议类型 `brainstorming`

2. **用户**（看完摘要后）：「翻译成英文给我看看」  
   **巫师**：返回英文摘要（已在 Mock 里）。

3. **用户**：「把待办导出到日历」  
   **系统**：弹出下载框 `meeting_todos.ics`  
   **巫师**：无需操作；`calendar_utils.py` 已生成文件。

4. **异常分支**（音频损坏时）  
   **系统**：Toast “音频解析失败，已为您切换文字输入模式”。  
   **巫师**：在后台把 `summary` 设为占位符 “【模拟】解析失败”。

---

## 场景 2：粘贴中文逐字稿 → 要点式摘要

1. **用户**：粘贴长文本，点「开始分析」。  
2. **系统**：loading 2 秒后出现要点式中文摘要 + 两条待办。  
3. **用户**：「请列出重点，给 bullet list」  
   **巫师**：返回 `• 要点 1\n• 要点 2\n• 要点 3`  
4. **用户**：「翻成英文」  
   **巫师**：已在 Mock 里配好英文版。

---

## 场景 3：中英混合对话

1. **用户**：上传 `sales_call.wav`（中英夹杂）。  
2. **系统**：显示双语摘要，会议类型 `sales_call`。  
3. **用户**：「帮我把中文部分翻译到英文摘要里」  
   **巫师**：返回全英文摘要。  
4. **用户**：「导出 DOCX」  
   **系统**：下载 `meeting_notes.docx`。

---

## Mock 查表（复制到 assets/mock_responses.csv）

pattern,reply_zh,reply_en
".*总结.*","【模拟】这是段落式中文摘要……","This is the English summary (mock)…"
".*要点.*","• 要点 1\n• 要点 2\n• 要点 3","• Point 1\n• Point 2\n• Point 3"
".*翻译.*英文.*","【模拟】以下为英文翻译版本……","Here is the English version (mock)…"
".*导出.*日历.*","【模拟】已生成 .ics 日历文件，请查收","ICS file generated (mock)"
".*导出.*DOCX.*","【模拟】已生成 Word 文档 meeting_notes.docx","DOCX exported (mock)"
".*失败.*","【模拟】解析失败，请重试或改用文字输入","Parse failed, please retry"
